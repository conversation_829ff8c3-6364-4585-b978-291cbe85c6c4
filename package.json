{"name": "orivox", "version": "0.1.0", "private": true, "dependencies": {"@gsap/react": "^2.1.0", "@lottiefiles/react-lottie-player": "^3.6.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "aos": "^2.3.4", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "daisyui": "^4.0.0", "framer-motion": "^12.11.3", "gsap": "^3.13.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^6.22.2", "react-scripts": "5.0.1", "react-tsparticles": "^2.12.2", "tailwindcss": "^3.3.5", "tsparticles": "^3.8.1", "web-vitals": "^3.5.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/aos": "^3.0.7", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "typescript": "^4.9.5"}}