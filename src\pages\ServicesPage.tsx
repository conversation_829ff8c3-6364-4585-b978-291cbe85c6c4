import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Player } from "@lottiefiles/react-lottie-player";
import { motion, AnimatePresence } from "framer-motion";
import Hero from "../components/Hero";
import FloatingContactButton from "../components/FloatingContactButton";

const ServicesPage = () => {
  const [activeTab, setActiveTab] = useState('divisions');
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setShowContent(true), 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    setShowContent(false);
    const timer = setTimeout(() => setShowContent(true), 100);
    return () => clearTimeout(timer);
  }, [activeTab]);

  // Helper function to render division lottie animations
  const renderDivisionLottie = (divisionId: number, className: string = "") => {
    const getAnimationSrc = () => {
      switch (divisionId) {
        case 1:
          return require("../asset/TRANS.json"); // Crystal View - Transparent displays
        case 2:
          return require("../asset/SandS.json"); // Script&Style - Web development
        case 3:
          return require("../asset/idea.json"); // Ideation Hub - Innovation
        default:
          return require("../asset/Service.json"); // Default service animation
      }
    };

    return (
      <div className={`${className} w-8 h-8 flex items-center justify-center`}>
        <Player
          autoplay
          loop
          src={getAnimationSrc()}
          style={{ width: "100%", height: "100%" }}
        />
      </div>
    );
  };

  // Division data
  const divisions = [
    {
      id: 1,
      title: 'Crystal View',
      subtitle: 'See Through. Think Beyond.',
      description: 'Developing transparent laptops, phones, kiosks & smart displays that seamlessly integrate digital content with the physical world.',
      link: '/transparent-display',
      bgColor: 'bg-gradient-to-br from-cyan-800 to-blue-800',
      ctaText: 'View Our Hardware Concepts',
      features: ["Transparent OLED Displays", "Holographic Interfaces", "Smart Glass Solutions", "XR Integration"],
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="w-10 h-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      id: 2,
      title: 'Script & Style',
      subtitle: 'Designs That Code. Code That Connects.',
      description: 'Providing custom websites, portfolios, digital branding, and apps for students, startups, and small industries.',
      link: '/scriptstyle',
      bgColor: 'bg-gradient-to-br from-blue-800 to-indigo-800',
      ctaText: 'Build with Us',
      features: ["Custom Web Development", "Digital Branding", "Student Portfolios", "Startup Solutions"],
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="w-10 h-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      id: 3,
      title: 'Ideation Hub',
      subtitle: 'Ideas Aren\'t Born. They\'re Engineered.',
      description: 'A creative space to incubate moonshot ideas, run deep-tech experiments, and mentor innovators in healthcare and space technology.',
      link: '/ideation-lab',
      bgColor: 'bg-gradient-to-br from-indigo-800 to-teal-800',
      ctaText: 'Submit Your Idea / Collaborate Now',
      features: ["Moonshot Incubation", "Deep-tech Experiments", "Innovation Mentoring", "Healthcare & Space Tech"],
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="w-10 h-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    }
  ];

  const whyOrivox = [
    {
      title: 'Transparent Future',
      description: 'Leading the revolution in transparent display technology that merges digital and physical worlds.',
      icon: '🔮'
    },
    {
      title: 'Student Innovation',
      description: 'Empowering the next generation of developers and designers through real-world experience.',
      icon: '🚀'
    },
    {
      title: 'Deep-tech Labs',
      description: 'Incubating breakthrough ideas in healthcare and space exploration technologies.',
      icon: '🧬'
    },
    {
      title: 'Interconnected Ecosystem',
      description: 'Three specialized divisions working in synergy to create comprehensive solutions.',
      icon: '🌐'
    }
  ];

  const testimonials = [
    {
      quote: "Orivox's transparent display technology is revolutionary. We're exploring implementations across our retail locations.",
      author: "Sarah Chen",
      role: "Innovation Director, RetailTech Solutions"
    },
    {
      quote: "The Script&Style platform gave me real-world experience while still in college. It transformed my career trajectory.",
      author: "Raj Patel",
      role: "UI/UX Designer & Former Student"
    }
  ];

  // Animation variants
  const headingVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const badgeVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: "backOut"
      }
    }
  };

  const staggerContainerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="flex flex-col min-h-screen overflow-hidden bg-gray-900">
      <Hero />

      {/* Tabs Section */}
      <section className="py-12 bg-gradient-to-b from-gray-900 to-gray-800 w-full">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="tabs tabs-boxed flex justify-center mb-8 bg-gray-800/70 p-1 backdrop-blur-sm max-w-xl mx-auto rounded-lg shadow-sm"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <motion.button
              className={`tab tab-lg flex-1 transition-all ${activeTab === 'divisions' ? 'tab-active bg-indigo-700 text-white' : 'hover:bg-gray-700 text-gray-300'}`}
              onClick={() => setActiveTab('divisions')}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
            >
              Our Divisions
            </motion.button>
            <motion.button
              className={`tab tab-lg flex-1 transition-all ${activeTab === 'why-orivox' ? 'tab-active bg-indigo-700 text-white' : 'hover:bg-gray-700 text-gray-300'}`}
              onClick={() => setActiveTab('why-orivox')}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
            >
              Why Orivox?
            </motion.button>
            <motion.button
              className={`tab tab-lg flex-1 transition-all ${activeTab === 'testimonials' ? 'tab-active bg-indigo-700 text-white' : 'hover:bg-gray-700 text-gray-300'}`}
              onClick={() => setActiveTab('testimonials')}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
            >
              Testimonials
            </motion.button>
          </motion.div>
        </div>
      </section>

      {/* Tab Content Container */}
      <section className="py-12 bg-gradient-to-b from-gray-800 to-gray-900 flex-grow w-full">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          {/* Divisions Section */}
          <AnimatePresence mode="wait">
            {activeTab === 'divisions' && showContent && (
              <motion.div
                key="divisions"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.5 }}
              >
                <motion.div
                  className="text-center mb-12"
                  variants={staggerContainerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <motion.div variants={badgeVariants}>
                    <div className="badge badge-lg badge-outline badge-primary mb-4 border-indigo-400 text-indigo-400">Specialized Divisions</div>
                  </motion.div>
                  <motion.h2
                    className="text-3xl md:text-4xl font-bold mb-4 font-orbitron text-gray-200"
                    variants={headingVariants}
                  >
                    Powering the <span className="text-indigo-400 relative inline-block">
                      Transparent Future
                      <motion.span
                        className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-blue-600 to-indigo-600"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                      />
                    </span>
                  </motion.h2>
                  <motion.p
                    className="text-gray-400 max-w-2xl mx-auto"
                    variants={itemVariants}
                  >
                    Three specialized divisions working in synergy to create breakthrough solutions across devices, code, and ideas.
                  </motion.p>
                </motion.div>

                <motion.div
                  className="grid grid-cols-1 md:grid-cols-3 gap-8"
                  variants={staggerContainerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  {divisions.map((division, index) => (
                    <motion.div
                      key={division.title}
                      className="card glass backdrop-blur-sm shadow-xl overflow-hidden border border-gray-700 h-full flex flex-col bg-gray-800/70"
                      variants={itemVariants}
                      whileHover={{
                        y: -5,
                        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.5)"
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      <figure className={`${division.bgColor} p-8 text-white flex justify-center items-center`}>
                        <motion.div
                          className="w-16 h-16 flex items-center justify-center"
                          initial={{ rotate: -10, scale: 0.9 }}
                          animate={{ rotate: 0, scale: 1 }}
                          transition={{ duration: 0.5, delay: 0.2 + (index * 0.1) }}
                        >
                          {division.icon}
                        </motion.div>
                      </figure>
                      <div className="card-body flex flex-col flex-grow">
                        <motion.h3
                          className="card-title text-xl font-orbitron text-gray-200"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.3 + (index * 0.1) }}
                        >
                          {division.title}
                        </motion.h3>
                        <motion.h4
                          className="text-lg font-semibold text-indigo-400 mb-2"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.4 + (index * 0.1) }}
                        >
                          {division.subtitle}
                        </motion.h4>
                        <motion.p
                          className="text-gray-400 flex-grow"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5, delay: 0.5 + (index * 0.1) }}
                        >
                          {division.description}
                        </motion.p>
                        <div className="card-actions justify-end mt-4">
                          <Link
                            to={division.link}
                            className="btn btn-sm bg-indigo-700 hover:bg-indigo-800 border-none text-white"
                          >
                            {division.ctaText}
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </Link>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </motion.div>
            )}

            {/* Why Orivox Section */}
            {activeTab === 'why-orivox' && showContent && (
              <motion.div
                key="why-orivox"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.5 }}
                className="py-8"
              >
                <motion.div
                  className="text-center mb-12"
                  variants={staggerContainerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <motion.div variants={badgeVariants}>
                    <div className="badge badge-lg badge-outline badge-primary mb-4 border-indigo-400 text-indigo-400">Our Advantage</div>
                  </motion.div>
                  <motion.h2
                    className="text-3xl md:text-4xl font-bold mb-4 font-orbitron text-gray-200"
                    variants={headingVariants}
                  >
                    Why Choose <span className="text-indigo-400 relative inline-block">
                      Orivox?
                      <motion.span
                        className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-blue-600 to-indigo-600"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                      />
                    </span>
                  </motion.h2>
                  <motion.p
                    className="text-gray-400 max-w-2xl mx-auto"
                    variants={itemVariants}
                  >
                    Whether you see the future, build it, or dream it — Orivox is your launchpad for innovation.
                  </motion.p>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                  {whyOrivox.map((item, index) => (
                    <motion.div
                      key={item.title}
                      className="card bg-gray-800 shadow-xl border border-gray-700"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      whileHover={{
                        y: -5,
                        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                      }}
                    >
                      <div className="card-body">
                        <div className="text-4xl mb-3">{item.icon}</div>
                        <h3 className="card-title text-xl font-orbitron text-gray-200">{item.title}</h3>
                        <p className="text-gray-400">{item.description}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Testimonials Section */}
            {activeTab === 'testimonials' && showContent && (
              <motion.div
                key="testimonials"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.5 }}
                className="py-8"
              >
                <motion.div
                  className="text-center mb-12"
                  variants={staggerContainerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <motion.div variants={badgeVariants}>
                    <div className="badge badge-lg badge-outline badge-primary mb-4 border-indigo-400 text-indigo-400">What People Say</div>
                  </motion.div>
                  <motion.h2
                    className="text-3xl md:text-4xl font-bold mb-4 font-orbitron text-gray-200"
                    variants={headingVariants}
                  >
                    Client <span className="text-indigo-400 relative inline-block">
                      Testimonials
                      <motion.span
                        className="absolute bottom-0 left-0 w-full h-[2px] bg-gradient-to-r from-blue-600 to-indigo-600"
                        initial={{ width: 0 }}
                        animate={{ width: "100%" }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                      />
                    </span>
                  </motion.h2>
                  <motion.p
                    className="text-gray-400 max-w-2xl mx-auto"
                    variants={itemVariants}
                  >
                    Hear from our partners and clients about their experience with Orivox's innovative solutions.
                  </motion.p>
                </motion.div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                  {testimonials.map((testimonial, index) => (
                    <motion.div
                      key={testimonial.author}
                      className="card bg-gray-800 shadow-xl border border-gray-700"
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      whileHover={{
                        y: -5,
                        boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)"
                      }}
                    >
                      <div className="card-body">
                        <svg className="w-10 h-10 text-indigo-400 mb-3" fill="currentColor" viewBox="0 0 32 32">
                          <path d="M10 8c-3.3 0-6 2.7-6 6v10h10V14H7c0-1.7 1.3-3 3-3V8zm18 0c-3.3 0-6 2.7-6 6v10h10V14h-7c0-1.7 1.3-3 3-3V8z"/>
                        </svg>
                        <p className="text-lg italic text-gray-300 mb-4">{testimonial.quote}</p>
                        <div className="mt-auto">
                          <h3 className="font-bold text-indigo-400">{testimonial.author}</h3>
                          <p className="text-sm text-gray-400">{testimonial.role}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-800 to-indigo-800 text-white w-full">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6 font-orbitron">Whether you see the future, build it, or dream it — Orivox is your launchpad.</h2>
            <p className="text-xl mb-8">
              Ready to transform your ideas into breakthrough solutions? Let's collaborate across our specialized divisions.
            </p>
            <motion.div
              className="flex flex-wrap justify-center gap-4"
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
            >
              <Link
                to="/contact"
                className="btn btn-lg bg-white text-indigo-800 hover:bg-gray-100 border-none font-semibold"
              >
                Book a Consultation
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-7-4c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
                </svg>
              </Link>
              <Link
                to="/about"
                className="btn btn-lg btn-outline border-white text-white hover:bg-white hover:text-indigo-800 font-semibold"
              >
                Join the Revolution
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </Link>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Floating Contact Button */}
      <FloatingContactButton />
    </div>
  );
};

export default ServicesPage;
